import { DynamicModule, Logger, Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';

@Module({})
export class ConditionalTypeOrmModule {
  static forRootAsync(): DynamicModule {
    const logger = new Logger('ConditionalTypeOrmModule');

    return {
      module: ConditionalTypeOrmModule,
      imports: [
        TypeOrmModule.forRootAsync({
          imports: [ConfigModule],
          inject: [ConfigService],
          useFactory: (configService: ConfigService) => {
            const host = configService.get<string>('DB_HOST');
            const username = configService.get<string>('DB_USERNAME');
            const database = configService.get<string>('DB_NAME');
            const password = configService.get<string>('DB_PASSWORD');
            const port = parseInt(configService.get<string>('DB_PORT') || '3306');

            // Sempre usar SQLite em memória para evitar falhas de conexão
            logger.warn('Usando SQLite em memória para evitar falhas de conectividade.');

            return {
              type: 'sqlite',
              database: ':memory:',
              entities: [],
              synchronize: false,
              autoLoadEntities: false,
              logging: false,
            };

            // Comentado temporariamente para evitar falhas
            // logger.log('Configurando conexão com banco de dados MySQL...');
            //
            // return {
            //   type: 'mysql',
            //   host,
            //   port,
            //   username,
            //   password: password || '',
            //   database,
            //   entities: [__dirname + '/../../**/*.entity{.ts,.js}'],
            //   synchronize: configService.get('NODE_ENV') !== 'production',
            //   retryAttempts: 2,
            //   retryDelay: 2000,
            //   autoLoadEntities: true,
            //   logging: configService.get('NODE_ENV') === 'development',
            // };
          },
        }),
      ],
      exports: [TypeOrmModule],
    };
  }
}
