import { <PERSON><PERSON><PERSON>, Column, PrimaryGeneratedColumn, ManyToOne, OneToMany, CreateDateColumn, UpdateDateColumn } from 'typeorm';
import { Cliente } from '../../cliente/entities/cliente.entity';
import { Assistencia } from '../../assistencia/entities/assistencia.entity';
import { Produto } from '../../produto/entities/produto.entity';
import { Status } from './status.entity';

@Entity()
export class Protocolo {
  @PrimaryGeneratedColumn()
  id: number;

  @ManyToOne(() => Cliente, cliente => cliente.protocolos)
  cliente: Cliente;

  @ManyToOne(() => Assistencia, assistencia => assistencia.protocolos)
  assistencia: Assistencia;

  @ManyToOne(() => Produto, produto => produto.protocolos)
  produto: Produto;

  @Column()
  nf: string;

  @Column()
  dataCompra: string;

  @Column()
  descricao: string;

  @Column()
  status: string;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  @OneToMany(() => Status, status => status.protocolo)
  historicoStatus: Status[];
}