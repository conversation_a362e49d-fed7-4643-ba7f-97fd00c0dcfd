import { ApiProperty } from '@nestjs/swagger';

export class AssistenciaDto {
  @ApiProperty({ example: 1, description: 'ID da assistência técnica' })
  id: number;

  @ApiProperty({ example: 'Assistência Técnica ABC', description: 'Nome da assistência' })
  nome: string;

  @ApiProperty({ example: '<EMAIL>', description: 'Email da assistência' })
  email: string;

  @ApiProperty({ example: '1134567890', description: 'Telefone da assistência' })
  telefone: string;

  @ApiProperty({ example: '01234567', description: 'CEP' })
  cep: string;

  @ApiProperty({ example: 'Sala 101', description: 'Endereco', required: true })
  endereco: string;

  @ApiProperty({ example: 'São Paulo', description: 'Cidade' })
  cidade: string;

  @ApiProperty({ example: 'SP', description: 'Estado (UF)' })
  uf: string;

  @ApiProperty({ example: 'ELETRODOMESTICO,CUIDADOS_PESSOAIS,INFORMATICA,CLIMATIZACAO,AUDIO,VIDEO', description: 'Servicos da assistencia' })
  servicos: string;
}