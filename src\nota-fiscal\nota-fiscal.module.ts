import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { NotaFiscalController } from './nota-fiscal.controller';
import { NotaFiscalService } from './nota-fiscal.service';
import { NotaFiscal } from './entities/nota-fiscal.entity';
import { NotaFiscalProduto } from './entities/nota-fiscal-produto.entity';
import { ClienteModule } from '../cliente/cliente.module';
import { ProdutoModule } from '../produto/produto.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([NotaFiscal, NotaFiscalProduto]),
    ClienteModule,
    ProdutoModule,
  ],
  controllers: [NotaFiscalController],
  providers: [NotaFiscalService],
  exports: [NotaFiscalService],
})
export class NotaFiscalModule {}
