import { <PERSON><PERSON><PERSON>, Column, PrimaryGeneratedColumn, OneToMany } from 'typeorm';
import { Protocolo } from '../../protocolo/entities/protocolo.entity';
import { NotaFiscalProduto } from '../../nota-fiscal/entities/nota-fiscal-produto.entity';

@Entity()
export class Produto {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  nome: string;

  @Column({ nullable: true, unique: true })
  codigo: string;

  @Column()
  descricao: string;

  @OneToMany(() => Protocolo, protocolo => protocolo.produto)
  protocolos: Protocolo[];

  @OneToMany(() => NotaFiscalProduto, notaFiscalProduto => notaFiscalProduto.produto)
  notaFiscalProdutos: NotaFiscalProduto[];
}


