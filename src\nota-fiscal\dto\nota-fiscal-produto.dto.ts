import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsNumber, Min } from 'class-validator';
import { ProdutoDto } from '../../produto/dto/produto.dto';

export class NotaFiscalProdutoDto {
  @ApiProperty({ example: 1, description: 'ID do relacionamento' })
  id: number;

  @ApiProperty({ type: ProdutoDto, description: 'Produto associado à nota fiscal' })
  produto: ProdutoDto;

  @ApiProperty({ example: 2, description: 'Quantidade do produto' })
  quantidade: number;

  @ApiProperty({ example: 599.99, description: 'Valor unitário do produto' })
  valorUnitario: number;

  @ApiProperty({ example: 1199.98, description: 'Valor total (quantidade * valorUnitario)' })
  valorTotal: number;
}

export class CreateNotaFiscalProdutoDto {
  @ApiProperty({ example: 1, description: 'ID do produto' })
  @IsNotEmpty()
  @IsNumber()
  produtoId: number;

  @ApiProperty({ example: 2, description: 'Quantidade do produto' })
  @IsNotEmpty()
  @IsNumber()
  @Min(1)
  quantidade: number;

  @ApiProperty({ example: 599.99, description: 'Valor unitário do produto' })
  @IsNotEmpty()
  @IsNumber()
  @Min(0)
  valorUnitario: number;
}