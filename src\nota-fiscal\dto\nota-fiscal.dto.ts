import { ApiProperty } from '@nestjs/swagger';
import { ClienteDto } from '../../cliente/dto/cliente.dto';
import { NotaFiscalProdutoDto } from './nota-fiscal-produto.dto';

export class NotaFiscalDto {
  @ApiProperty({ example: 1, description: 'ID da nota fiscal' })
  id: number;

  @ApiProperty({ example: 'NF123456', description: 'Número da nota fiscal' })
  numero: string;

  @ApiProperty({ example: 1299.99, description: 'Valor total da nota fiscal' })
  valor: number;

  @ApiProperty({ example: '2023-07-10', description: 'Data de emissão' })
  dataEmissao: Date;

  @ApiProperty({ type: ClienteDto, description: 'Cliente associado à nota fiscal' })
  cliente: ClienteDto;

  @ApiProperty({ type: [NotaFiscalProdutoDto], description: 'Produtos da nota fiscal' })
  notaFiscalProdutos: NotaFiscalProdutoDto[];

  @ApiProperty({ example: '2023-07-15T14:30:00Z', description: 'Data de criação do registro' })
  createdAt: Date;
}
