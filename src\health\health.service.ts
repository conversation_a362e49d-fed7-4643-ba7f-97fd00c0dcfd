import { Injectable, Logger } from '@nestjs/common';
import { InjectConnection } from '@nestjs/typeorm';
import { Connection } from 'typeorm';
import { DatabaseConfigService } from '../common/services/database-config.service';

@Injectable()
export class HealthService {
  private readonly logger = new Logger(HealthService.name);

  constructor(
    @InjectConnection() private readonly connection: Connection,
    private readonly databaseConfigService: DatabaseConfigService,
  ) {}

  async getHealthStatus() {
    const databaseStatus = await this.getDatabaseStatus();
    
    return {
      status: 'ok',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      environment: process.env.NODE_ENV || 'development',
      version: '1.0.0',
      database: databaseStatus,
      services: {
        api: 'healthy',
        integrations: 'healthy',
      },
    };
  }

  async getDatabaseStatus() {
    try {
      if (!this.databaseConfigService.isDatabaseConfigured()) {
        return {
          status: 'not_configured',
          message: 'Banco de dados não configurado',
          configured: false,
          connected: false,
        };
      }

      if (!this.connection || !this.connection.isConnected) {
        return {
          status: 'disconnected',
          message: 'Não conectado ao banco de dados',
          configured: true,
          connected: false,
        };
      }

      await this.connection.query('SELECT 1');
      
      return {
        status: 'healthy',
        message: 'Conectado ao banco de dados',
        configured: true,
        connected: true,
        database: (this.connection.options as any).database,
        host: (this.connection.options as any).host,
      };
    } catch (error) {
      this.logger.error('Erro ao verificar status do banco:', error.message);
      
      return {
        status: 'unhealthy',
        message: `Erro na conexão: ${error.message}`,
        configured: this.databaseConfigService.isDatabaseConfigured(),
        connected: false,
        error: error.message,
      };
    }
  }
}
