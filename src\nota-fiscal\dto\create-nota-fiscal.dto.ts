import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsNumber, IsString, IsDateString, IsArray, ValidateNested, ArrayMinSize } from 'class-validator';
import { Type } from 'class-transformer';
import { CreateNotaFiscalProdutoDto } from './nota-fiscal-produto.dto';

export class CreateNotaFiscalDto {
  @ApiProperty({ example: 'NF123456', description: 'Número da nota fiscal' })
  @IsNotEmpty()
  @IsString()
  numero: string;

  @ApiProperty({ example: '2023-07-10', description: 'Data de emissão' })
  @IsNotEmpty()
  @IsDateString()
  dataEmissao: string;

  @ApiProperty({ example: 1, description: 'ID do cliente' })
  @IsNotEmpty()
  @IsNumber()
  clienteId: number;

  @ApiProperty({ 
    type: [CreateNotaFiscalProdutoDto], 
    description: 'Lista de produtos da nota fiscal' 
  })
  @IsArray()
  @ValidateNested({ each: true })
  @ArrayMinSize(1)
  @Type(() => CreateNotaFiscalProdutoDto)
  produtos: CreateNotaFiscalProdutoDto[];
}
