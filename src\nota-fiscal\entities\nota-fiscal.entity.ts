import { <PERSON><PERSON><PERSON>, Column, PrimaryGeneratedColumn, ManyToOne, OneToMany, CreateDateColumn } from 'typeorm';
import { Cliente } from '../../cliente/entities/cliente.entity';
import { NotaFiscalProduto } from './nota-fiscal-produto.entity';

@Entity()
export class NotaFiscal {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  numero: string;

  @Column({ type: 'decimal', precision: 10, scale: 2 })
  valor: number;

  @Column()
  dataEmissao: Date;

  @ManyToOne(() => Cliente, cliente => cliente.id)
  cliente: Cliente;

  @OneToMany(() => NotaFiscalProduto, notaFiscalProduto => notaFiscalProduto.notaFiscal, { 
    cascade: true,
    eager: true
  })
  notaFiscalProdutos: NotaFiscalProduto[];

  @CreateDateColumn()
  createdAt: Date;
}
