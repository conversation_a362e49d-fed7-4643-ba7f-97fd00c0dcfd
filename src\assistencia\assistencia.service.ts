import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Assistencia } from './entities/assistencia.entity';
import { AssistenciaDto } from './dto/assistencia.dto';
import { PaginationQueryDto, PaginatedResponseDto } from '../common/dto/pagination.dto';

@Injectable()
export class AssistenciaService {
  constructor(
    @InjectRepository(Assistencia)
    private assistenciaRepository: Repository<Assistencia>,
  ) {}

  async findAll(
    paginationDto: PaginationQueryDto,
    nome?: string,
    cep?: string,
    endereco?: string,
    uf?: string,
    servicos?: string,
    email?: string,
  ): Promise<PaginatedResponseDto<AssistenciaDto>> {
    const { page, limit } = paginationDto;
    const skip = (page - 1) * limit;

    let queryBuilder = this.assistenciaRepository.createQueryBuilder('assistencia');

    if (nome) {
      queryBuilder = queryBuilder.andWhere('assistencia.nome LIKE :nome', { nome: `%${nome}%` });
    }

    if (cep) {
      queryBuilder = queryBuilder.andWhere('assistencia.cep = :cep', { cep });
    }

    if (endereco) {
      queryBuilder = queryBuilder.andWhere('assistencia.endereco LIKE :endereco', { endereco: `%${endereco}%` });
    }

    if (uf) {
      queryBuilder = queryBuilder.andWhere('assistencia.uf = :uf', { uf });
    }

    if (email) {
      queryBuilder = queryBuilder.andWhere('assistencia.email LIKE :email', { email: `%${email}%` });
    }

    if (servicos) {
      const servicosArray = servicos.split(',').map(s => s.trim());
      const servicosConditions = servicosArray.map((_, index) =>
        `assistencia.servicos LIKE :servico${index}`
      ).join(' OR ');

      const servicosParams = servicosArray.reduce((params, servico, index) => {
        params[`servico${index}`] = `%${servico}%`;
        return params;
      }, {});

      queryBuilder = queryBuilder.andWhere(`assistencia.servicos IS NOT NULL AND assistencia.servicos != '' AND (${servicosConditions})`, servicosParams);
    }

    const [assistencias, total] = await queryBuilder
      .skip(skip)
      .take(limit)
      .getManyAndCount();

    return {
      data: assistencias,
      meta: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      },
    };
  }
}