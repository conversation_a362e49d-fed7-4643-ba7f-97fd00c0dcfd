import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Assistencia } from './entities/assistencia.entity';
import { AssistenciaDto } from './dto/assistencia.dto';
import { PaginationQueryDto, PaginatedResponseDto } from '../common/dto/pagination.dto';

@Injectable()
export class AssistenciaService {
  constructor(
    @InjectRepository(Assistencia)
    private assistenciaRepository: Repository<Assistencia>,
  ) {}

  async findNearby(
    paginationDto: PaginationQueryDto,
    cep: string,
  ): Promise<PaginatedResponseDto<AssistenciaDto>> {
    const { page, limit } = paginationDto;
    const skip = (page - 1) * limit;

    // Aqui você implementaria a lógica para calcular a distância com base no CEP
    // Por simplicidade, estamos apenas retornando todas as assistências com uma distância fictícia
    const [assistencias, total] = await this.assistenciaRepository.findAndCount({
      skip,
      take: limit,
    });

    // Adicionar a distância calculada a cada assistência
    const assistenciasComDistancia = assistencias.map(assistencia => ({
      ...assistencia,
      distancia: this.calcularDistancia(cep, assistencia.cep),
    }));

    // Ordenar por distância
    assistenciasComDistancia.sort((a, b) => a.distancia - b.distancia);

    return {
      data: assistenciasComDistancia,
      meta: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  // Método fictício para calcular distância entre CEPs
  private calcularDistancia(cepOrigem: string, cepDestino: string): number {
    // Em uma implementação real, você usaria uma API de geolocalização
    // Por enquanto, retornamos um valor aleatório entre 1 e 20 km
    return Math.random() * 19 + 1;
  }
}