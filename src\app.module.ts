import { <PERSON>du<PERSON> } from '@nestjs/common';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { ClienteModule } from './cliente/cliente.module';
import { ProdutoModule } from './produto/produto.module';
import { AssistenciaModule } from './assistencia/assistencia.module';
import { ProtocoloModule } from './protocolo/protocolo.module';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule } from '@nestjs/config';
import { NotaFiscalModule } from './nota-fiscal/nota-fiscal.module';
import { GestaowebModule } from './gestaoweb/gestaoweb.module';

@Module({
  imports: [
    ConfigModule.forRoot(),
    TypeOrmModule.forRoot({
      type: 'mysql',
      host: process.env.DB_HOST,
      port: parseInt(process.env.DB_PORT || '3306'),
      username: process.env.DB_USERNAME,
      password: process.env.DB_PASSWORD,
      database: process.env.DB_NAME,
      entities: [__dirname + '/**/*.entity{.ts,.js}'],
      synchronize: process.env.NODE_ENV !== 'production',
    }),
    ClienteModule,
    ProdutoModule,
    AssistenciaModule,
    ProtocoloModule,
    NotaFiscalModule,
    ProdutoModule,
    GestaowebModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
