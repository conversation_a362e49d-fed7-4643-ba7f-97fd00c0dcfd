import { Module } from '@nestjs/common';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { ClienteModule } from './cliente/cliente.module';
import { ProdutoModule } from './produto/produto.module';
import { AssistenciaModule } from './assistencia/assistencia.module';
import { ProtocoloModule } from './protocolo/protocolo.module';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { NotaFiscalModule } from './nota-fiscal/nota-fiscal.module';
import { GestaowebModule } from './gestaoweb/gestaoweb.module';
import { PostoModule } from './posto/posto.module';
import { LinhaModule } from './linha/linha.module';
import { FamiliaModule } from './familia/familia.module';
import { DatabaseConfigService } from './common/services/database-config.service';
import { HealthModule } from './health/health.module';

@Module({
  imports: [
    ConfigModule.forRoot(),
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      inject: [DatabaseConfigService],
      useFactory: async (databaseConfigService: DatabaseConfigService) => {
        return databaseConfigService.createTypeOrmOptions();
      },
    }),
    ClienteModule,
    ProdutoModule,
    AssistenciaModule,
    ProtocoloModule,
    NotaFiscalModule,
    GestaowebModule,
    PostoModule,
    LinhaModule,
    FamiliaModule,
    HealthModule,
  ],
  controllers: [AppController],
  providers: [AppService, DatabaseConfigService],
})
export class AppModule {}
