import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AssistenciaController } from './assistencia.controller';
import { AssistenciaService } from './assistencia.service';
import { Assistencia } from './entities/assistencia.entity';

@Module({
  imports: [TypeOrmModule.forFeature([Assistencia])],
  controllers: [AssistenciaController],
  providers: [AssistenciaService],
  exports: [AssistenciaService],
})
export class AssistenciaModule {}