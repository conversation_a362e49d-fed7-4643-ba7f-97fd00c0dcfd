import { Controller, Get, Query } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiQuery } from '@nestjs/swagger';
import { AssistenciaDto } from './dto/assistencia.dto';
import { PaginationQueryDto, PaginatedResponseDto } from '../common/dto/pagination.dto';
import { AssistenciaService } from './assistencia.service';

@ApiTags('assistencias')
@Controller('assistencias')
export class AssistenciaController {
  constructor(private readonly assistenciaService: AssistenciaService) {}

  @Get()
  @ApiOperation({ summary: 'Listar assistências técnicas com filtros' })
  @ApiQuery({ name: 'page', required: false, description: 'Número da página' })
  @ApiQuery({ name: 'limit', required: false, description: 'Limite de itens por página' })
  @ApiQuery({ name: 'nome', required: false, description: 'Filtrar por nome da assistência' })
  @ApiQuery({ name: 'cep', required: false, description: 'Filtrar por CEP' })
  @ApiQuery({ name: 'endereco', required: false, description: 'Filtrar por endereço' })
  @ApiQuery({ name: 'uf', required: false, description: 'Filtrar por UF' })
  @ApiQuery({ name: 'email', required: false, description: 'Filtrar por email' })
  @ApiQuery({ name: 'servicos', required: false, description: 'Filtrar por serviços (separados por vírgula)' })
  @ApiResponse({ status: 200, description: 'Lista de assistências técnicas retornada com sucesso' })
  findAll(
    @Query() paginationDto: PaginationQueryDto,
    @Query('nome') nome?: string,
    @Query('cep') cep?: string,
    @Query('endereco') endereco?: string,
    @Query('uf') uf?: string,
    @Query('email') email?: string,
    @Query('servicos') servicos?: string,
  ): Promise<PaginatedResponseDto<AssistenciaDto>> {
    return this.assistenciaService.findAll(paginationDto, nome, cep, endereco, uf, servicos, email);
  }
}