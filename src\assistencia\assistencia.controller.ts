import { Controller, Get, Query } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiQuery } from '@nestjs/swagger';
import { AssistenciaDto } from './dto/assistencia.dto';
import { PaginationQueryDto, PaginatedResponseDto } from '../common/dto/pagination.dto';
import { AssistenciaService } from './assistencia.service';

@ApiTags('assistencias')
@Controller('assistencias')
export class AssistenciaController {
  constructor(private readonly assistenciaService: AssistenciaService) {}

  @Get('localizar')
  @ApiOperation({ summary: 'Localizar assistências técnicas próximas por CEP' })
  @ApiQuery({ name: 'page', required: false, description: 'Número da página' })
  @ApiQuery({ name: 'limit', required: false, description: 'Limite de itens por página' })
  @ApiQuery({ name: 'cep', required: true, description: 'CEP para busca de proximidade' })
  @ApiResponse({ status: 200, description: 'Lista de assistências técnicas retornada com sucesso' })
  findNearby(
    @Query() paginationDto: PaginationQueryDto,
    @Query('cep') cep: string,
  ): Promise<PaginatedResponseDto<AssistenciaDto>> {
    return this.assistenciaService.findNearby(paginationDto, cep);
  }
}