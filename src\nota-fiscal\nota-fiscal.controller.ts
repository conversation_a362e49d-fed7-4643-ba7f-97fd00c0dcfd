import { Controller, Get, Post, Body, Param, Query, Delete } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiQuery } from '@nestjs/swagger';
import { NotaFiscalService } from './nota-fiscal.service';
import { CreateNotaFiscalDto } from './dto/create-nota-fiscal.dto';
import { NotaFiscalDto } from './dto/nota-fiscal.dto';
import { PaginationQueryDto, PaginatedResponseDto } from '../common/dto/pagination.dto';
import { CreateNotaFiscalProdutoDto } from './dto/nota-fiscal-produto.dto';

@ApiTags('notas_fiscais')
@Controller('notas_fiscais')
export class NotaFiscalController {
  constructor(private readonly notaFiscalService: NotaFiscalService) {}

  @Post()
  @ApiOperation({ summary: 'Criar uma nova nota fiscal' })
  @ApiResponse({ status: 201, description: 'Nota fiscal criada com sucesso', type: NotaFiscalDto })
  create(@Body() createNotaFiscalDto: CreateNotaFiscalDto): Promise<NotaFiscalDto> {
    return this.notaFiscalService.create(createNotaFiscalDto);
  }

  @Post(':id/produtos')
  @ApiOperation({ summary: 'Adicionar produto a uma nota fiscal existente' })
  @ApiParam({ name: 'id', description: 'ID da nota fiscal' })
  @ApiResponse({ status: 201, description: 'Produto adicionado com sucesso' })
  async adicionarProduto(
    @Param('id') id: string,
    @Body() createNotaFiscalProdutoDto: CreateNotaFiscalProdutoDto
  ): Promise<void> {
    await this.notaFiscalService.adicionarProduto(+id, createNotaFiscalProdutoDto);
  }

  @Delete(':id/produtos/:produtoId')
  @ApiOperation({ summary: 'Remover produto de uma nota fiscal' })
  @ApiParam({ name: 'id', description: 'ID da nota fiscal' })
  @ApiParam({ name: 'produtoId', description: 'ID do relacionamento nota fiscal-produto' })
  @ApiResponse({ status: 200, description: 'Produto removido com sucesso' })
  async removerProduto(
    @Param('id') id: string,
    @Param('produtoId') produtoId: string
  ): Promise<void> {
    await this.notaFiscalService.removerProduto(+id, +produtoId);
  }

  @Get()
  @ApiOperation({ summary: 'Listar todas as notas fiscais com paginação e filtros' })
  @ApiQuery({ name: 'page', required: false, description: 'Número da página' })
  @ApiQuery({ name: 'limit', required: false, description: 'Limite de itens por página' })
  @ApiQuery({ name: 'numero', required: false, description: 'Filtrar por número da nota fiscal' })
  @ApiQuery({ name: 'cpfCnpj', required: false, description: 'Filtrar por CPF/CNPJ do cliente' })
  @ApiQuery({ name: 'clienteId', required: false, description: 'Filtrar por ID do cliente' })
  @ApiResponse({ status: 200, description: 'Lista de notas fiscais retornada com sucesso' })
  findAll(
    @Query() paginationDto: PaginationQueryDto,
    @Query('numero') numero?: string,
    @Query('cpfCnpj') cpfCnpj?: string,
    @Query('clienteId') clienteId?: number,
  ): Promise<PaginatedResponseDto<NotaFiscalDto>> {
    return this.notaFiscalService.findAll(paginationDto, numero, cpfCnpj, clienteId);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Buscar uma nota fiscal pelo ID' })
  @ApiParam({ name: 'id', description: 'ID da nota fiscal' })
  @ApiResponse({ status: 200, description: 'Nota fiscal encontrada', type: NotaFiscalDto })
  @ApiResponse({ status: 404, description: 'Nota fiscal não encontrada' })
  findOne(@Param('id') id: string): Promise<NotaFiscalDto> {
    return this.notaFiscalService.findOne(+id);
  }
}
