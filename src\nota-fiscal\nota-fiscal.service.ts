import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { NotaFiscal } from './entities/nota-fiscal.entity';
import { NotaFiscalProduto } from './entities/nota-fiscal-produto.entity';
import { CreateNotaFiscalDto } from './dto/create-nota-fiscal.dto';
import { NotaFiscalDto } from './dto/nota-fiscal.dto';
import { PaginationQueryDto, PaginatedResponseDto } from '../common/dto/pagination.dto';
import { ClienteService } from '../cliente/cliente.service';
import { ProdutoService } from '../produto/produto.service';
import { CreateNotaFiscalProdutoDto } from './dto/nota-fiscal-produto.dto';

@Injectable()
export class NotaFiscalService {
  constructor(
    @InjectRepository(NotaFiscal)
    private notaFiscalRepository: Repository<NotaFiscal>,
    @InjectRepository(NotaFiscalProduto)
    private notaFiscalProdutoRepository: Repository<NotaFiscalProduto>,
    private clienteService: ClienteService,
    private produtoService: ProdutoService,
  ) {}

  async create(createNotaFiscalDto: CreateNotaFiscalDto): Promise<NotaFiscalDto> {
    // Buscar cliente
    const cliente = await this.clienteService.findOne(createNotaFiscalDto.clienteId);

    // Calcular valor total da nota fiscal
    let valorTotal = 0;
    for (const produtoDto of createNotaFiscalDto.produtos) {
      valorTotal += produtoDto.quantidade * produtoDto.valorUnitario;
    }

    // Criar nota fiscal
    const notaFiscal = this.notaFiscalRepository.create({
      numero: createNotaFiscalDto.numero,
      valor: valorTotal,
      dataEmissao: new Date(createNotaFiscalDto.dataEmissao),
      cliente: { id: cliente.id },
      notaFiscalProdutos: [],
    });

    // Salvar a nota fiscal
    const savedNotaFiscal = await this.notaFiscalRepository.save(notaFiscal);

    // Adicionar produtos à nota fiscal
    for (const produtoDto of createNotaFiscalDto.produtos) {
      await this.adicionarProduto(savedNotaFiscal.id, produtoDto);
    }

    // Buscar a nota fiscal completa com relações
    return this.findOne(savedNotaFiscal.id);
  }

  async adicionarProduto(notaFiscalId: number, createNotaFiscalProdutoDto: CreateNotaFiscalProdutoDto): Promise<void> {
    // Buscar nota fiscal
    const notaFiscal = await this.notaFiscalRepository.findOne({
      where: { id: notaFiscalId },
      relations: ['notaFiscalProdutos'],
    });

    if (!notaFiscal) {
      throw new NotFoundException(`Nota fiscal com ID ${notaFiscalId} não encontrada`);
    }

    // Buscar produto
    const produto = await this.produtoService.findOne(createNotaFiscalProdutoDto.produtoId);

    // Calcular valor total
    const valorTotal = createNotaFiscalProdutoDto.quantidade * createNotaFiscalProdutoDto.valorUnitario;

    // Criar relação nota fiscal-produto
    const notaFiscalProduto = this.notaFiscalProdutoRepository.create({
      notaFiscal: { id: notaFiscalId },
      produto: { id: produto.id },
      quantidade: createNotaFiscalProdutoDto.quantidade,
      valorUnitario: createNotaFiscalProdutoDto.valorUnitario,
      valorTotal,
    });

    await this.notaFiscalProdutoRepository.save(notaFiscalProduto);

    // Atualizar valor total da nota fiscal
    notaFiscal.valor = Number((Number(notaFiscal.valor) + valorTotal).toFixed(2));

    await this.notaFiscalRepository.save(notaFiscal);
  }

  async removerProduto(notaFiscalId: number, notaFiscalProdutoId: number): Promise<void> {
    // Buscar nota fiscal
    const notaFiscal = await this.notaFiscalRepository.findOne({
      where: { id: notaFiscalId },
    });

    if (!notaFiscal) {
      throw new NotFoundException(`Nota fiscal com ID ${notaFiscalId} não encontrada`);
    }

    // Buscar relação nota fiscal-produto
    const notaFiscalProduto = await this.notaFiscalProdutoRepository.findOne({
      where: { id: notaFiscalProdutoId, notaFiscal: { id: notaFiscalId } },
    });

    if (!notaFiscalProduto) {
      throw new NotFoundException(`Produto com ID ${notaFiscalProdutoId} não encontrado na nota fiscal`);
    }

    // Atualizar valor total da nota fiscal
    notaFiscal.valor = Number((Number(notaFiscal.valor) - Number(notaFiscalProduto.valorTotal)).toFixed(2));

    await this.notaFiscalRepository.save(notaFiscal);

    // Remover relação
    await this.notaFiscalProdutoRepository.remove(notaFiscalProduto);
  }

  async findAll(
    paginationDto: PaginationQueryDto,
    numero?: string,
    cpfCnpj?: string,
    clienteId?: number,
  ): Promise<PaginatedResponseDto<NotaFiscalDto>> {
    const { page, limit } = paginationDto;
    const skip = (page - 1) * limit;

    let queryBuilder = this.notaFiscalRepository
      .createQueryBuilder('notaFiscal')
      .leftJoinAndSelect('notaFiscal.cliente', 'cliente')
      .leftJoinAndSelect('notaFiscal.notaFiscalProdutos', 'notaFiscalProdutos')
      .leftJoinAndSelect('notaFiscalProdutos.produto', 'produto');

    if (numero) {
      queryBuilder = queryBuilder.andWhere('notaFiscal.numero = :numero', { numero });
    }

    if (clienteId) {
      queryBuilder = queryBuilder.andWhere('cliente.id = :clienteId', { clienteId });
    }

    if (cpfCnpj) {
      queryBuilder = queryBuilder.andWhere(
        '(cliente.cpf = :cpfCnpj OR cliente.cnpj = :cpfCnpj)',
        { cpfCnpj }
      );
    }

    const [notasFiscais, total] = await queryBuilder
      .skip(skip)
      .take(limit)
      .orderBy('notaFiscal.createdAt', 'DESC')
      .getManyAndCount();

    // Mapear para o DTO
    const notasFiscaisDto = notasFiscais.map(notaFiscal => {
      const notaFiscalDto = new NotaFiscalDto();
      Object.assign(notaFiscalDto, notaFiscal);
      return notaFiscalDto;
    });

    return {
      data: notasFiscaisDto,
      meta: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  async findOne(id: number): Promise<NotaFiscalDto> {
    const notaFiscal = await this.notaFiscalRepository.findOne({
      where: { id },
      relations: ['cliente', 'notaFiscalProdutos', 'notaFiscalProdutos.produto'],
    });

    if (!notaFiscal) {
      throw new NotFoundException(`Nota fiscal com ID ${id} não encontrada`);
    }

    // Mapear para o DTO
    const notaFiscalDto = new NotaFiscalDto();
    Object.assign(notaFiscalDto, notaFiscal);
    
    return notaFiscalDto;
  }
}
