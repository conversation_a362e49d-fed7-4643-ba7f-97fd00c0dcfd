import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { TypeOrmModuleOptions } from '@nestjs/typeorm';

@Injectable()
export class DatabaseConfigService {
  private readonly logger = new Logger(DatabaseConfigService.name);

  constructor(private readonly configService: ConfigService) {}

  createTypeOrmOptions(): TypeOrmModuleOptions {
    const host = this.configService.get('DB_HOST');
    const username = this.configService.get('DB_USERNAME');
    const database = this.configService.get('DB_NAME');

    if (!host || !username || !database) {
      this.logger.warn('Configurações do banco de dados não encontradas. Usando configuração mínima.');
      
      return {
        type: 'mysql',
        host: 'localhost',
        port: 3306,
        username: 'root',
        password: '',
        database: 'temp_db',
        entities: [],
        synchronize: false,
        autoLoadEntities: false,
        retryAttempts: 0,
        retryDelay: 1000,
        logging: false,
      };
    }

    this.logger.log('Configurando conexão com banco de dados...');
    
    return {
      type: 'mysql',
      host,
      port: parseInt(this.configService.get('DB_PORT') || '3306'),
      username,
      password: this.configService.get('DB_PASSWORD'),
      database,
      entities: [__dirname + '/../../**/*.entity{.ts,.js}'],
      synchronize: this.configService.get('NODE_ENV') !== 'production',
      retryAttempts: 3,
      retryDelay: 3000,
      autoLoadEntities: true,
      logging: this.configService.get('NODE_ENV') === 'development',
    };
  }

  isDatabaseConfigured(): boolean {
    const host = this.configService.get('DB_HOST');
    const username = this.configService.get('DB_USERNAME');
    const database = this.configService.get('DB_NAME');
    
    return !!(host && username && database);
  }
}
