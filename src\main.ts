import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { Logger } from '@nestjs/common';
import { DatabaseErrorInterceptor } from './common/interceptors/database-error.interceptor';

async function bootstrap() {
  const logger = new Logger('Bootstrap');

  try {
    logger.log('Iniciando aplicação...');

    const app = await NestFactory.create(AppModule, {
      logger: ['error', 'warn', 'log'],
    });

    app.useGlobalInterceptors(new DatabaseErrorInterceptor());

    const config = new DocumentBuilder()
      .setTitle('Amvox API')
      .setDescription('API de integração Amvox com Telecontrol')
      .setVersion('1.0')
      .build();

    const document = SwaggerModule.createDocument(app, config);
    SwaggerModule.setup('api-docs', app, document);

    const port = process.env.PORT || 3000;
    await app.listen(port);

    logger.log(`Aplicação iniciada com sucesso na porta ${port}`);
    logger.log(`Documentação disponível em: http://localhost:${port}/api-docs`);

  } catch (error) {
    logger.error('Erro ao iniciar aplicação:', error.message);
    logger.error('Stack trace:', error.stack);

    if (error.message?.includes('ECONNREFUSED') ||
        error.message?.includes('database') ||
        error.message?.includes('ENOTFOUND') ||
        error.code === 'ENOTFOUND') {
      logger.warn('Erro de conexão com banco de dados detectado. A aplicação continuará sem banco.');

      try {
        const app = await NestFactory.create(AppModule, {
          logger: ['error', 'warn', 'log'],
        });

        app.useGlobalInterceptors(new DatabaseErrorInterceptor());

        const config = new DocumentBuilder()
          .setTitle('Amvox API')
          .setDescription('API de integração Amvox com Telecontrol (Modo sem banco)')
          .setVersion('1.0')
          .build();

        const document = SwaggerModule.createDocument(app, config);
        SwaggerModule.setup('api-docs', app, document);

        const port = process.env.PORT || 3000;
        await app.listen(port);

        logger.log(`Aplicação iniciada em modo sem banco na porta ${port}`);
        logger.log(`Documentação disponível em: http://localhost:${port}/api-docs`);

      } catch (fallbackError) {
        logger.error('Erro crítico ao iniciar aplicação:', fallbackError.message);
        process.exit(1);
      }
    } else {
      logger.error('Erro crítico na inicialização. Encerrando aplicação.');
      process.exit(1);
    }
  }
}

bootstrap();
