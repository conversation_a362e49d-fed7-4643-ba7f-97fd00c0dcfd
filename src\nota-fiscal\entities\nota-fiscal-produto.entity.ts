import { <PERSON><PERSON><PERSON>, Column, PrimaryGenerated<PERSON><PERSON>umn, ManyTo<PERSON>ne, Join<PERSON><PERSON>umn } from 'typeorm';
import { NotaFiscal } from './nota-fiscal.entity';
import { Produto } from '../../produto/entities/produto.entity';

@Entity()
export class NotaFiscalProduto {
  @PrimaryGeneratedColumn()
  id: number;

  @ManyToOne(() => NotaFiscal, notaFiscal => notaFiscal.notaFiscalProdutos, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'notaFiscalId' })
  notaFiscal: NotaFiscal;

  @ManyToOne(() => Produto, produto => produto.notaFiscalProdutos)
  @JoinColumn({ name: 'produtoId' })
  produto: Produto;

  @Column({ type: 'decimal', precision: 10, scale: 2 })
  quantidade: number;

  @Column({ type: 'decimal', precision: 10, scale: 2 })
  valorUnitario: number;

  @Column({ type: 'decimal', precision: 10, scale: 2 })
  valorTotal: number;
}