import {
  Injectable,
  NestInterceptor,
  ExecutionContext,
  CallHandler,
  HttpException,
  HttpStatus,
  Logger,
} from '@nestjs/common';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';

@Injectable()
export class DatabaseErrorInterceptor implements NestInterceptor {
  private readonly logger = new Logger(DatabaseErrorInterceptor.name);

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    return next.handle().pipe(
      catchError((error) => {
        if (this.isDatabaseError(error)) {
          this.logger.error('Erro de banco de dados detectado:', error.message);
          
          const request = context.switchToHttp().getRequest();
          const endpoint = `${request.method} ${request.url}`;
          
          this.logger.warn(`Endpoint ${endpoint} falhou devido a problema no banco de dados`);
          
          throw new HttpException(
            {
              statusCode: HttpStatus.SERVICE_UNAVAILABLE,
              message: 'Serviço temporariamente indisponível devido a problemas de conectividade com o banco de dados',
              error: 'Database Connection Error',
              timestamp: new Date().toISOString(),
              path: request.url,
            },
            HttpStatus.SERVICE_UNAVAILABLE,
          );
        }
        
        return throwError(() => error);
      }),
    );
  }

  private isDatabaseError(error: any): boolean {
    const databaseErrorMessages = [
      'ECONNREFUSED',
      'ENOTFOUND',
      'ETIMEDOUT',
      'Connection lost',
      'connect ECONNREFUSED',
      'getaddrinfo ENOTFOUND',
      'ER_ACCESS_DENIED_ERROR',
      'ER_BAD_DB_ERROR',
      'Repository',
      'QueryFailedError',
    ];

    const errorMessage = error?.message || '';
    const errorCode = error?.code || '';
    
    return databaseErrorMessages.some(msg => 
      errorMessage.includes(msg) || errorCode.includes(msg)
    );
  }
}
