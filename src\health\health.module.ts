import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { HealthController } from './health.controller';
import { HealthService } from './health.service';
import { DatabaseConfigService } from '../common/services/database-config.service';

@Module({
  imports: [ConfigModule],
  controllers: [HealthController],
  providers: [HealthService, DatabaseConfigService],
  exports: [HealthService],
})
export class HealthModule {}
