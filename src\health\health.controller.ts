import { Controller, Get } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { HealthService } from './health.service';

@ApiTags('health')
@Controller('health')
export class HealthController {
  constructor(private readonly healthService: HealthService) {}

  @Get()
  @ApiOperation({ summary: 'Verificar status da aplicação' })
  @ApiResponse({ status: 200, description: 'Status da aplicação' })
  async getHealth() {
    return await this.healthService.getHealthStatus();
  }

  @Get('database')
  @ApiOperation({ summary: 'Verificar status do banco de dados' })
  @ApiResponse({ status: 200, description: 'Status do banco de dados' })
  async getDatabaseHealth() {
    return await this.healthService.getDatabaseStatus();
  }
}
